package com.volvo.maintain.application.maintainlead.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("二手车查询车辆工单信息及保修类型代码入参实体类")
public class RepairOrderAndClaimTypeReqDto implements Serializable {
	
    private static final long serialVersionUID = 7262488384516099125L;

	@ApiModelProperty(value = "经销商code")
    private String ownerCode;

    @ApiModelProperty(value = "工单号")
    private String roNo;

    @ApiModelProperty(value = "车架号", required = true)
    private String vin;
    
    @ApiModelProperty(value = "开始时间")
    private String roCreateDateStart;
    
    @ApiModelProperty(value = "结束时间")
    private String roCreateDateEnd;

    @ApiModelProperty(value = "当前页")
    private Integer pageNum;
    
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;
}
