package com.volvo.maintain.application.maintainlead.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.InvitationFollowParamsDto;
import com.volvo.maintain.application.maintainlead.dto.InviteVehicleRecordDetailDto;
import com.volvo.maintain.application.maintainlead.dto.MaintainLeadDto;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainLeadFeign;
import com.yonyou.cyx.function.exception.ServiceBizException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 功能描述：养修线索实现
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Slf4j
@Service
public class MaintainLeadServiceImpl implements MaintainLeadService {
    private final DomainMaintainLeadFeign domainMaintainLeadFeign;

    public MaintainLeadServiceImpl(DomainMaintainLeadFeign domainMaintainLeadFeign) {
        this.domainMaintainLeadFeign = domainMaintainLeadFeign;
    }

    /**
     * 功能描述：查询邀约线索列表
     *
     * @param paramDto 养修线索dto对象
     * @return Page<MaintainLeadDto> 养修线索列表
     */
    @Override
    public Page<MaintainLeadDto> getInviteVehicleRecord(InvitationFollowParamsDto paramDto) {
        RestResultResponse<Page<MaintainLeadDto>> restResultResponse = domainMaintainLeadFeign.getInviteVehicleRecord(paramDto);
        if (restResultResponse == null || restResultResponse.getResultCode() != HttpStatus.OK.value()) {
            log.error("feign调用查询邀约线索列表失败");
            throw new ServiceBizException("feign调用查询邀约线索列表失败");
        }
        log.info("getInviteVehicleRecord打印返回参数：{}", JSON.toJSONString(restResultResponse));
        return restResultResponse.getData();
    }

    /**
     * 功能描述：保存跟进记录
     *
     * @param param 查询邀约跟进记录查询dto
     * @return int  返回保存结果
     */
    @Override
    public int saveInviteVehicleRecord(InviteVehicleRecordDetailDto param) {
        RestResultResponse<Boolean> restResultResponse = domainMaintainLeadFeign.saveInviteVehicleRecord(param);
        if (restResultResponse == null || restResultResponse.getResultCode() != HttpStatus.OK.value()) {
            log.error("feign保存跟进记录失败");
            throw new ServiceBizException("feign保存跟进记录失败");
        }
        log.info("saveInviteVehicleRecord保存跟进记录返回结果：{}", JSON.toJSONString(restResultResponse));
        if (Objects.equals(Boolean.TRUE, restResultResponse.getData())) {
            return 1;
        } else {
            return 0;
        }
    }
}
