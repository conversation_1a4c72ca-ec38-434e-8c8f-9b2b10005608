package com.volvo.maintain.application.maintainlead.dto.dtcclues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Objects;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("DTC线索类别导出")
public class ExportDtcCluesCategoryDto extends DtcCluesCategoryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Confirm状态 （0-全部，1-是, 2-否）")
    protected String confirmStatusString;

    @ApiModelProperty(value = "Indicator状态 （0-全部，1-是, 2-否）")
    protected String indicatorStatusString;

    @ApiModelProperty(value = "序号")
    private Integer serialNumber;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        ExportDtcCluesCategoryDto that = (ExportDtcCluesCategoryDto) o;
        return Objects.equals(confirmStatusString, that.confirmStatusString) && Objects.equals(indicatorStatusString, that.indicatorStatusString);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), confirmStatusString, indicatorStatusString);
    }
}
