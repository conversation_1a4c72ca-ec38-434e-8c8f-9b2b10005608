package com.volvo.maintain.application.maintainlead.service.dtcclues.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.DtcCluesCategoryDto;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.ImportDtcCluesCategoryDto;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.ImportResultInfoDto;
import com.volvo.maintain.application.maintainlead.service.dtcclues.DataProcessingService;
import com.volvo.maintain.infrastructure.enums.DtcConfirmStatusEnum;
import com.volvo.maintain.infrastructure.enums.DtcIndicatorStatusEnum;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataProcessingServiceImpl implements DataProcessingService {
    @Override
    public List<ImportDtcCluesCategoryDto> parseImportDtcCluesCategoryData(MultipartFile importFile) {
        ImportParams importParams = new ImportParams();
        importParams.setNeedVerify(true);

        List<ImportDtcCluesCategoryDto> categoryDtoList;
        try {
            categoryDtoList = ExcelImportUtil.importExcel(importFile.getInputStream(), ImportDtcCluesCategoryDto.class, importParams);

            log.info("dtc clues import size：{}", categoryDtoList.size());
            log.info("dtc clues import origin data：{}", JSON.toJSONString(categoryDtoList));
        } catch (Exception e) {
            log.error("parse dtc clues import data exception",e);
            throw new ServiceBizException("DTC线索导入解析数据异常");
        }
        return categoryDtoList;
    }

    @Override
    public List<ImportResultInfoDto> validateAndConvertImportData(List<ImportDtcCluesCategoryDto> categoryDtoList, Integer category) {
        if (CollectionUtils.isEmpty(categoryDtoList)) {
            throw new ServiceBizException("DTC线索导入数据为空");
        }
        List<ImportResultInfoDto> importResultInfoList = basicInfoValidate(categoryDtoList, category);
        if (CollectionUtils.isNotEmpty(importResultInfoList)) {
            return importResultInfoList;
        }

        int distinctSize = (int) categoryDtoList.stream().map(ImportDtcCluesCategoryDto::getSerialNumber).distinct().count();
        if (distinctSize != categoryDtoList.size()) {
            throw new ServiceBizException("序号不能重复!");
        }

        Map<String, Long> uniqueEcuDtcMap = categoryDtoList.stream().collect(Collectors.groupingBy(v -> v.getEcu() + "-" + v.getDtc(), Collectors.counting()));
        if (!uniqueEcuDtcMap.isEmpty()) {
            for (Map.Entry<String, Long> countMap : uniqueEcuDtcMap.entrySet()) {
                if (countMap.getValue() > 1) {
                    throw new ServiceBizException("ECU+DTC要保证唯一性!");
                }
            }
        }

        categoryDtoList.forEach(v -> {
            Integer indicatorStatus = DtcIndicatorStatusEnum.getCodeByName(v.getIndicatorStatusString());
            Integer confirmStatus = DtcConfirmStatusEnum.getCodeByName(v.getConfirmStatusString());
            if (Objects.isNull(confirmStatus)) {
                throw new ServiceBizException("Confirm生成线索值不合法");
            }
            if (Objects.isNull(indicatorStatus)) {
                throw new ServiceBizException("Indicator生成线索值不合法");
            }
            v.setIndicatorStatus(indicatorStatus);
            v.setConfirmStatus(confirmStatus);
        });
        return Collections.emptyList();
    }

    @Override
    public void validateInsertParams(DtcCluesCategoryDto dtcCluesCategoryDto) {
        if (Objects.isNull(dtcCluesCategoryDto.getEcu())) {
            throw new ServiceBizException("ECU不能为空");
        }

        if (Objects.isNull(dtcCluesCategoryDto.getDtc())) {
            throw new ServiceBizException("DTC不能为空");
        }

        if (Objects.isNull(dtcCluesCategoryDto.getConfirmStatus())) {
            throw new ServiceBizException("Confirm状态不能为空");
        }

        if (Objects.isNull(dtcCluesCategoryDto.getIndicatorStatus())) {
            throw new ServiceBizException("Indicator状态不能为空");
        }

        if (Objects.isNull(dtcCluesCategoryDto.getVehicleModelIds())) {
            throw new ServiceBizException("车型不能为空");
        }
    }

    @Override
    public List<ExcelExportColumn> buildExcelColumn(Integer category) {
        List<ExcelExportColumn> exportColumnList = new ArrayList<>();
        exportColumnList.add(new ExcelExportColumn("serialNumber", "序号"));
        exportColumnList.add(new ExcelExportColumn("faultCategory", "故障类别"));
        exportColumnList.add(new ExcelExportColumn("ecu", "ECU"));
        exportColumnList.add(new ExcelExportColumn("dtc", "DTC"));
        exportColumnList.add(new ExcelExportColumn("confirmStatusString", "Confirm生成线索"));
        exportColumnList.add(new ExcelExportColumn("indicatorStatusString", "Indicator生成线索"));
        if (category == 1) {
            exportColumnList.add(new ExcelExportColumn("priority", "优先级"));
        }
        return exportColumnList;
    }

    @Override
    public String getLoginUserId() {
        try {
            CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
            return (Objects.isNull(loginInfoDto)) ? null : loginInfoDto.getUserId();
        } catch (Exception e) {
            log.error("query current login info exception: ", e);
            return null;
        }
    }

    private List<ImportResultInfoDto> basicInfoValidate(List<ImportDtcCluesCategoryDto> categoryDtoList, Integer category) {
        return categoryDtoList.stream().map(v -> {
            List<String> remarkList = new ArrayList<>();
            if (category == 1) {
                validGenerateCategoryParams(v, remarkList);
            }
            if (StringUtils.isEmpty(v.getEcu())) {
                remarkList.add("ECU不能为空");
            }
            if (StringUtils.isEmpty(v.getDtc())) {
                remarkList.add("DTC不能为空");
            }
            if (StringUtils.isEmpty(v.getConfirmStatusString())) {
                remarkList.add("Confirm生成线索不能为空");
            }
            if (StringUtils.isEmpty(v.getIndicatorStatusString())) {
                remarkList.add("Indicator生成线索不能为空");
            }
            String serialNumber = v.getSerialNumber();
            if (StringUtils.isEmpty(serialNumber)) {
                remarkList.add("序号不能为空");
            }
            try {
                Integer.valueOf(serialNumber);
            } catch (Exception e) {
                remarkList.add("序号必须为数值");
            }
            if (CollectionUtils.isNotEmpty(remarkList)) {
                return new ImportResultInfoDto(remarkList, v.getRowNum());
            }
            return null;
        }).filter(v -> !Objects.isNull(v)).collect(Collectors.toList());
    }

    private void validGenerateCategoryParams(ImportDtcCluesCategoryDto v, List<String> remarkList) {
        if (StringUtils.isEmpty(v.getFaultCategory())) {
            remarkList.add("故障类别不能为空");
        }
        if (Objects.isNull(v.getPriority())) {
            remarkList.add("优先级不能为空");
        }
        if (v.getPriority() < 1 || v.getPriority() > 999) {
            remarkList.add("优先级只能为1-999之间的整数");
        }
    }
}
