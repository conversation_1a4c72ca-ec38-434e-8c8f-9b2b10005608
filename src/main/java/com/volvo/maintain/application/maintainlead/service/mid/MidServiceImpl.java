package com.volvo.maintain.application.maintainlead.service.mid;

import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.AllModeDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.DataDto;
import com.volvo.maintain.infrastructure.gateway.MidEndBasicdataCenterFeign;
import com.yonyou.cyx.framework.util.bean.ApplicationContextHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MidServiceImpl implements MidService{
    @Resource
    private MidEndBasicdataCenterFeign midEndBasicdataCenterFeign;

    @Override
    @Cacheable(value = "allModeList", key = "'allModeList'", cacheManager = "cacheManager", unless = "#result == null")
    public List<AllModeDto> selectAllModeList() {
        try {
            ResponseDto<List<AllModeDto>> res =  midEndBasicdataCenterFeign.allModeList(new DataDto());
            if(res == null){
                log.info("selectAllModeList,res == null");
                return null;
            }
            List<AllModeDto> list = res.getData();
            if(CollectionUtils.isEmpty(list)){
                log.info("selectAllModeList,list is empty");
                return null;
            }
            log.info("selectAllModeList,list:{}", list.size());
            return list;
        } catch (Exception e) {
            log.error("selectAllModeList,查询全量车型列表时发生异常", e);
            return null;
        }
    }

    @Override
    public Map<String, String> getModelIdToNameMap() {
        //使用代理调用selectAllModeList
        MidService proxy = ApplicationContextHelper.getBeanByType(MidService.class);
        List<AllModeDto> allModeList = proxy.selectAllModeList();
        Map<String, String> modelIdToNameMap = new HashMap<>(allModeList.size());
        if (CollectionUtils.isEmpty(allModeList)) {
            log.warn("getModelIdToNameMap,获取全量车型列表为空，后续车型ID无法匹配名称");
            return modelIdToNameMap;
        }
        // 遍历封装映射关系
        for (AllModeDto modeDto : allModeList) {
            // 避免modeDto.getId()为null导致的NPE
            if (StringUtils.isEmpty(modeDto.getModelCode())) {
                log.warn("getModelIdToNameMap,存在车型ID为空的异常数据，车型信息：{}", modeDto);
                continue;
            }
            modelIdToNameMap.put(modeDto.getModelCode(), modeDto.getModelName());
        }
        return modelIdToNameMap;
    }
}
