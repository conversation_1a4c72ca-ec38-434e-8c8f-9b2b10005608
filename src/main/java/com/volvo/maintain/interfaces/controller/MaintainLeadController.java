package com.volvo.maintain.interfaces.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.InvitationFollowParamsDto;
import com.volvo.maintain.application.maintainlead.dto.InviteVehicleRecordDetailDto;
import com.volvo.maintain.application.maintainlead.dto.MaintainLeadDto;
import com.volvo.maintain.application.maintainlead.service.MaintainLeadService;
import com.volvo.utils.LoginInfoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 功能描述：养修线索控制层
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@RestController
@RequestMapping("/invitationFollow")
@Api(value = "养修线索adapter层", tags = {"养修线索adapter层"})
@Slf4j
public class MaintainLeadController {
    @Autowired
    protected RedissonClient redissonClient;

    private final MaintainLeadService maintainLeadService;

    public MaintainLeadController(MaintainLeadService maintainLeadService) {
        this.maintainLeadService = maintainLeadService;
    }

    /**
     * 功能描述：查询邀约线索列表
     *
     * @param paramDto 养修线索dto对象
     * @return Page<MaintainLeadDto> 养修线索列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDto", name = "paramDto", value = "养修线索dto对象", required = true)
    })
    @ApiOperation(value = "查询邀约线索列表", notes = "功能描述：查询邀约线索列表", httpMethod = "POST")
    @PostMapping("/list")
    public Page<MaintainLeadDto> getInviteVehicleRecord(@RequestBody InvitationFollowParamsDto paramDto) {
        log.info("getInviteVehicleRecord  enter args:{}", JSON.toJSONString(paramDto));
        //测试代码
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        log.info("登录信息:{}", JSON.toJSONString(currentLoginInfo));
        return maintainLeadService.getInviteVehicleRecord(paramDto);
    }

    /**
     * 功能描述：保存跟进记录
     *
     * @param param 查询邀约跟进记录查询dto
     * @return int  返回保存结果
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteVehicleRecordDetailDto", name = "param", value = "查询邀约跟进记录查询dto", required = true)
    })
    @ApiOperation(value = "保存跟进记录", notes = "功能描述：保存跟进记录", httpMethod = "POST")
    @PostMapping("/saveInviteVehicleRecord")
    public int saveInviteVehicleRecord(@RequestBody InviteVehicleRecordDetailDto param) {
        log.info("saveInviteVehicleRecord enter args:{}", JSON.toJSONString(param));
        return maintainLeadService.saveInviteVehicleRecord(param);
    }
}
