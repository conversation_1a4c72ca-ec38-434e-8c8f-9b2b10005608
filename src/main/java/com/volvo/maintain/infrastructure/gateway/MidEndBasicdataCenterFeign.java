package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.*;
import com.volvo.maintain.interfaces.vo.TmConfigVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "mid-end-basicdata-center", url = "${baseUrl.midEndBasicdataCenter}")
public interface MidEndBasicdataCenterFeign {

    @GetMapping(value = "/basicdata/config/id/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<TmConfigVO> selectConfigById(@PathVariable("id") Long id);

    /**
     * 基于车型id 查询 中文名称
     * @param modelIdDTO id
     * @return 中文名称
     */
    @PostMapping(path = "/basicdata/model/list/modelIds", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<List<ModelInfoDto>> queryModelNameById(@RequestBody ModelIdDto modelIdDTO);

    /**
     * 查询所有车型
     * @return AllModeDto
     */
    @PostMapping(path = "/basicdata/model/list")
    ResponseDto<List<AllModeDto>> allModeList(@RequestBody DataDto dto );
}
