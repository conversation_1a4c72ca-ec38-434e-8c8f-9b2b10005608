package com.volvo.maintain.infrastructure.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * vip用户群组及其业务相关信息
 */
@Getter
public enum BizGroupEnum {

    BIZ_GROUP_T("t", "重点维修组", "360_FACE_CUSTOMER_REMINDER", "联系技术", "2", "请联系VCDC技术部，对车辆电池进行检查。"),
    BIZ_GROUP_LB("lb", "重点关照组", "360_FACE_CUSTOMER_CARE", "012客户", "3", "012客户车辆，请关注！"),
    BIZ_GROUP_BX("bx", "保修召回组", "", "QB", "", ""),
    BIZ_GROUP_90("90", "重点服务组", "360_FACE_CUSTOMER_ATTENTION", "召回关注", "", "");

    private static Map<String, BizGroupEnum> map = new HashMap<>();

    /**
     * 弹窗type，文案map
     */
    private static Map<String, String> remindTypeMap = new HashMap<>();

    static {
        Arrays.stream(BizGroupEnum.values()).forEach(modeEnum -> map.put(modeEnum.getCode(), modeEnum));
        Arrays.stream(BizGroupEnum.values()).forEach(modeEnum -> remindTypeMap.put(modeEnum.getRemindType(), modeEnum.getRemindText()));
    }

    @Getter
    private final String code;
    private final String name;
    private final String mailSinceType;
    private final String mailTitleFillInfo;
    private final String remindType;
    private final String remindText;

    BizGroupEnum(String code, String name, String mailSinceType, String mailTitleFillInfo, String remindType, String remindText) {
        this.code = code;
        this.name = name;
        this.mailSinceType = mailSinceType;
        this.mailTitleFillInfo = mailTitleFillInfo;
        this.remindType = remindType;
        this.remindText = remindText;
    }

    /**
     * 功能描述：解析
     *
     * @param serial 错误编码
     * @return ErrorEnum 返回对应的数据
     */
    public static BizGroupEnum resolve(String code) {
        return map.get(code);
    }

    /**
     * 获取弹窗文案
     */
    public static String getTextByRemindType(String code) {
        return remindTypeMap.get(code);
    }


}
